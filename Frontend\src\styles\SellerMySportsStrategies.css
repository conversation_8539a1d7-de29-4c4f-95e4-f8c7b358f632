/* SellerMySportsStrategies.css */

.video-status-container {
  padding: var(--section-padding);
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
}

.video-table {
  width: 100%;

  font-size: var(--basefont);
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  overflow: hidden;
}

.video-table th {
  padding: 12px 10px;
  text-align: left;

  vertical-align: middle;
}
.video-table td {
  padding: 12px 10px;
  text-align: left;
  border-top: 1px solid var(--light-gray);
  vertical-align: middle;
}
.video-doc {
  display: flex;
  align-items: center;
  gap: 10px;
}

.video-doc img {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.video-doc span {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 22px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  background-color: var(--light-gray);
  border-radius: 22px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: 0.4s;
}

.slider::before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 3px;
  bottom: 3px;
  background-color: var(--white);
  border-radius: 50%;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: var(--btn-color);
}

input:checked + .slider::before {
  transform: translateX(18px);
}

.slider.round {
  border-radius: 34px;
}

.action-icon {
  color: var(--btn-color);
  font-size: 18px;
  cursor: pointer;
}
